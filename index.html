<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تكنوفلاش - نظام إدارة نقاط البيع</title>
    
    <!-- خطوط عربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- ملفات CSS -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-cash-register"></i>
                    <h1>تكنوفلاش</h1>
                    <p>نظام إدارة نقاط البيع</p>
                </div>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="input-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-wrapper">
                        <input type="password" id="password" placeholder="أدخل كلمة المرور" required>
                        <i class="fas fa-lock"></i>
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
                
                <div class="login-info">
                    <p><i class="fas fa-info-circle"></i> كلمة المرور الافتراضية: ١٢٣</p>
                </div>
            </form>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" class="main-app hidden">
        <!-- شريط التنقل العلوي -->
        <header class="top-navbar">
            <div class="navbar-brand">
                <i class="fas fa-cash-register"></i>
                <span class="company-name">تكنوفلاش</span>
            </div>
            
            <div class="navbar-center">
                <div class="current-time" id="currentTime"></div>
                <div class="current-date" id="currentDate"></div>
            </div>
            
            <div class="navbar-actions">
                <button class="theme-toggle" id="themeToggle" title="تبديل الثيم">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="logout-btn" id="logoutBtn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <aside class="sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active">
                        <a href="#" class="nav-link" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة المعلومات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="sales">
                            <i class="fas fa-shopping-cart"></i>
                            <span>المبيعات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="products">
                            <i class="fas fa-box"></i>
                            <span>المنتجات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="customers">
                            <i class="fas fa-users"></i>
                            <span>العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="suppliers">
                            <i class="fas fa-truck"></i>
                            <span>الموردين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="purchases">
                            <i class="fas fa-shopping-bag"></i>
                            <span>المشتريات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="debts">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>الديون والمدفوعات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="reports">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="settings">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="backup">
                            <i class="fas fa-database"></i>
                            <span>النسخ الاحتياطي</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content" id="mainContent">
            <!-- سيتم تحميل المحتوى هنا ديناميكياً -->
        </main>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">تأكيد العملية</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">هل أنت متأكد من هذه العملية؟</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="confirmCancel">إلغاء</button>
                <button class="btn btn-danger" id="confirmOk">تأكيد</button>
            </div>
        </div>
    </div>

    <!-- نافذة التنبيه -->
    <div id="alertModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">تنبيه</h3>
            </div>
            <div class="modal-body">
                <p id="alertMessage">رسالة التنبيه</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="alertOk">موافق</button>
            </div>
        </div>
    </div>

    <!-- نافذة ضبط المصنع -->
    <div id="factoryResetModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>ضبط المصنع</h3>
                <button class="modal-close" onclick="app.hideModal('factoryResetModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p><strong>تحذير:</strong> هذه العملية ستحذف جميع البيانات نهائياً ولا يمكن التراجع عنها!</p>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="createBackupBeforeReset" checked>
                        إنشاء نسخة احتياطية قبل الحذف
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="resetSettings">
                        حذف الإعدادات أيضاً
                    </label>
                </div>
                
                <div class="form-group">
                    <label>للتأكيد، اكتب "ضبط المصنع" في الحقل أدناه:</label>
                    <input type="text" id="factoryResetConfirmation" class="form-control" 
                           placeholder="ضبط المصنع" oninput="validateFactoryResetConfirmation()">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="app.hideModal('factoryResetModal')">إلغاء</button>
                <button class="btn btn-secondary" id="factoryResetButton" disabled onclick="performFactoryReset()">
                    <i class="fas fa-exclamation-triangle"></i> ضبط المصنع
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة التقدم -->
    <div id="progressModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="progressTitle">جاري المعالجة...</h3>
            </div>
            <div class="modal-body">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">٠%</div>
                </div>
                <p id="progressDetails">جاري التحضير...</p>
            </div>
        </div>
    </div>

    <!-- شريط التحميل -->
    <div id="loadingBar" class="loading-bar hidden">
        <div class="loading-progress"></div>
    </div>

    <!-- الإشعارات -->
    <div id="notifications" class="notifications"></div>

    <!-- مكتبات خارجية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- ملفات JavaScript -->
    <script src="database.js"></script>
    <script src="barcode-generator.js"></script>
    <script src="main.js"></script>
    <script src="app.js"></script>
    <script src="dashboard.js"></script>
    <script src="products.js"></script>
    <script src="customers.js"></script>
    <script src="suppliers.js"></script>
    <script src="sales.js"></script>
    <script src="purchases.js"></script>
    <script src="debts.js"></script>
    <script src="reports.js"></script>
    <script src="settings.js"></script>
    <script src="backup.js"></script>
</body>
</html>
