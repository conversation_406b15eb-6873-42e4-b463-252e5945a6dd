/**
 * نظام الطباعة الحرارية - تكنوفلاش POS
 * يدعم طباعة الفواتير وملصقات الباركود على الطابعات الحرارية
 */

/**
 * أوامر ESC/POS للطابعات الحرارية
 */
const ESC_POS_COMMANDS = {
    // أوامر التهيئة
    INIT: '\x1B\x40',                    // تهيئة الطابعة
    RESET: '\x1B\x40',                   // إعادة تعيين
    
    // أوامر النص
    BOLD_ON: '\x1B\x45\x01',            // تشغيل الخط العريض
    BOLD_OFF: '\x1B\x45\x00',           // إيقاف الخط العريض
    UNDERLINE_ON: '\x1B\x2D\x01',       // تشغيل التسطير
    UNDERLINE_OFF: '\x1B\x2D\x00',      // إيقاف التسطير
    DOUBLE_HEIGHT: '\x1B\x21\x10',      // ضعف الارتفاع
    DOUBLE_WIDTH: '\x1B\x21\x20',       // ضعف العرض
    NORMAL_SIZE: '\x1B\x21\x00',        // الحجم العادي
    
    // أوامر المحاذاة
    ALIGN_LEFT: '\x1B\x61\x00',         // محاذاة يسار
    ALIGN_CENTER: '\x1B\x61\x01',       // محاذاة وسط
    ALIGN_RIGHT: '\x1B\x61\x02',        // محاذاة يمين
    
    // أوامر القطع
    CUT_FULL: '\x1D\x56\x00',           // قطع كامل
    CUT_PARTIAL: '\x1D\x56\x01',        // قطع جزئي
    CUT_FEED: '\x1D\x56\x41',           // قطع مع تغذية الورق
    
    // أوامر التغذية
    LINE_FEED: '\x0A',                  // سطر جديد
    FORM_FEED: '\x0C',                  // صفحة جديدة
    FEED_LINES: (n) => `\x1B\x64\x${n.toString(16).padStart(2, '0')}`, // تغذية n أسطر
    
    // أوامر الباركود
    BARCODE_HEIGHT: (h) => `\x1D\x68\x${h.toString(16).padStart(2, '0')}`, // ارتفاع الباركود
    BARCODE_WIDTH: (w) => `\x1D\x77\x0${w}`,  // عرض الباركود
    BARCODE_POSITION: '\x1D\x48\x02',   // موضع النص تحت الباركود
    BARCODE_CODE128: '\x1D\x6B\x49',    // باركود Code128
    BARCODE_EAN13: '\x1D\x6B\x43',      // باركود EAN13
};

/**
 * إعدادات الطباعة الحرارية
 */
const THERMAL_SETTINGS = {
    // عرض الورق بالأحرف (58mm = 32 حرف تقريباً)
    PAPER_WIDTH_58MM: 32,
    PAPER_WIDTH_80MM: 48,
    
    // إعدادات الباركود
    BARCODE_HEIGHT: 50,
    BARCODE_WIDTH: 2,
    
    // إعدادات الملصق
    LABEL_WIDTH: 32,
    LABEL_HEIGHT: 8,
};

/**
 * طباعة فاتورة حرارية بحجم 58mm
 */
function printThermalInvoice(saleData) {
    try {
        console.log('🖨️ بدء طباعة الفاتورة الحرارية...');
        
        const sale = typeof saleData === 'string' ? db.getSale(saleData) : saleData;
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        const customer = sale.customerId !== 'guest' ? db.getCustomer(sale.customerId) : null;
        const settings = db.getSettings();
        const companyInfo = settings.company || {};
        
        // إنشاء رقم فاتورة
        const invoiceNumber = sale.invoiceNumber || `INV-${new Date().getFullYear()}-${sale.id.substring(0, 8)}`;
        const currentDate = new Date(sale.date);
        
        // بناء محتوى الفاتورة الحرارية
        let thermalContent = '';
        
        // تهيئة الطابعة
        thermalContent += ESC_POS_COMMANDS.INIT;
        
        // رأسية الفاتورة - اسم المتجر
        thermalContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        thermalContent += ESC_POS_COMMANDS.DOUBLE_HEIGHT;
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += (companyInfo.companyName || 'متجر تكنوفلاش') + '\n';
        thermalContent += ESC_POS_COMMANDS.NORMAL_SIZE;
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;
        
        // معلومات الشركة
        if (companyInfo.phone) {
            thermalContent += 'هاتف: ' + companyInfo.phone + '\n';
        }
        if (companyInfo.address) {
            thermalContent += companyInfo.address + '\n';
        }
        if (companyInfo.taxNumber) {
            thermalContent += 'الرقم الضريبي: ' + companyInfo.taxNumber + '\n';
        }
        
        // خط فاصل
        thermalContent += ESC_POS_COMMANDS.ALIGN_LEFT;
        thermalContent += '================================\n';
        
        // معلومات الفاتورة
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += 'فاتورة مبيعات\n';
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;
        thermalContent += `رقم الفاتورة: ${invoiceNumber}\n`;
        thermalContent += `التاريخ: ${formatDateForThermal(currentDate)}\n`;
        thermalContent += `الوقت: ${currentDate.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}\n`;
        
        // معلومات العميل
        if (customer) {
            thermalContent += `العميل: ${customer.name}\n`;
            if (customer.phone) {
                thermalContent += `الهاتف: ${customer.phone}\n`;
            }
        }
        
        thermalContent += '================================\n';
        
        // رأسية الجدول
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += padText('المنتج', 16) + padText('ك', 4) + padText('السعر', 6) + padText('المجموع', 6) + '\n';
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;
        thermalContent += '--------------------------------\n';
        
        // عناصر الفاتورة
        let subtotal = 0;
        sale.items.forEach(item => {
            const product = db.getProduct(item.productId);
            const productName = product ? product.name : 'منتج غير معروف';
            const itemTotal = item.quantity * item.price;
            subtotal += itemTotal;
            
            // اسم المنتج (قد يحتاج لأكثر من سطر)
            const truncatedName = truncateText(productName, 16);
            thermalContent += padText(truncatedName, 16);
            thermalContent += padText(item.quantity.toString(), 4);
            thermalContent += padText(formatCurrencyForThermal(item.price), 6);
            thermalContent += padText(formatCurrencyForThermal(itemTotal), 6) + '\n';
        });
        
        thermalContent += '--------------------------------\n';
        
        // المجاميع
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += padText('المجموع الفرعي:', 20) + padText(formatCurrencyForThermal(subtotal), 12) + '\n';
        
        // الضرائب
        if (sale.tax && sale.tax > 0) {
            thermalContent += padText('الضريبة:', 20) + padText(formatCurrencyForThermal(sale.tax), 12) + '\n';
        }
        
        // الخصم
        if (sale.discount && sale.discount > 0) {
            thermalContent += padText('الخصم:', 20) + padText('-' + formatCurrencyForThermal(sale.discount), 12) + '\n';
        }
        
        // المجموع النهائي
        thermalContent += '================================\n';
        thermalContent += ESC_POS_COMMANDS.DOUBLE_HEIGHT;
        thermalContent += padText('الإجمالي:', 16) + padText(formatCurrencyForThermal(sale.total), 16) + '\n';
        thermalContent += ESC_POS_COMMANDS.NORMAL_SIZE;
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;
        
        // طريقة الدفع
        thermalContent += '================================\n';
        thermalContent += `طريقة الدفع: ${getPaymentMethodText(sale.paymentMethod)}\n`;
        
        if (sale.amountPaid && sale.amountPaid !== sale.total) {
            thermalContent += `المبلغ المدفوع: ${formatCurrencyForThermal(sale.amountPaid)}\n`;
            const change = sale.amountPaid - sale.total;
            if (change > 0) {
                thermalContent += `الباقي: ${formatCurrencyForThermal(change)}\n`;
            }
        }
        
        // تذييل الفاتورة
        thermalContent += '\n';
        thermalContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        thermalContent += 'شكراً لتسوقكم معنا\n';
        thermalContent += 'يمكن إرجاع المنتجات خلال 7 أيام\n';
        thermalContent += '\n';
        thermalContent += `طُبعت في: ${new Date().toLocaleString('ar-SA')}\n`;
        thermalContent += 'نظام تكنوفلاش POS\n';
        
        // تغذية الورق وقطع
        thermalContent += '\n\n\n';
        thermalContent += ESC_POS_COMMANDS.CUT_FEED;
        
        // إرسال للطباعة
        sendToThermalPrinter(thermalContent, 'فاتورة مبيعات');
        
        console.log('✅ تم إنشاء الفاتورة الحرارية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في طباعة الفاتورة الحرارية:', error);
        app.showAlert('خطأ', 'فشل في طباعة الفاتورة الحرارية: ' + error.message);
    }
}

/**
 * طباعة ملصق باركود للمنتج
 */
function printProductBarcodeLabel(productId) {
    try {
        console.log('🏷️ بدء طباعة ملصق الباركود...');
        
        const product = db.getProduct(productId);
        if (!product) {
            throw new Error('لم يتم العثور على المنتج');
        }
        
        if (!product.barcode) {
            throw new Error('المنتج لا يحتوي على باركود');
        }
        
        // بناء محتوى الملصق
        let labelContent = '';
        
        // تهيئة الطابعة
        labelContent += ESC_POS_COMMANDS.INIT;
        
        // اسم المنتج
        labelContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        labelContent += ESC_POS_COMMANDS.BOLD_ON;
        const truncatedName = truncateText(product.name, 24);
        labelContent += truncatedName + '\n';
        labelContent += ESC_POS_COMMANDS.BOLD_OFF;
        
        // كود المنتج إن وجد
        if (product.code) {
            labelContent += `كود: ${product.code}\n`;
        }
        
        // السعر
        labelContent += ESC_POS_COMMANDS.BOLD_ON;
        labelContent += `السعر: ${formatCurrencyForThermal(product.price)}\n`;
        labelContent += ESC_POS_COMMANDS.BOLD_OFF;
        
        // إعدادات الباركود
        labelContent += ESC_POS_COMMANDS.BARCODE_HEIGHT(THERMAL_SETTINGS.BARCODE_HEIGHT);
        labelContent += ESC_POS_COMMANDS.BARCODE_WIDTH(THERMAL_SETTINGS.BARCODE_WIDTH);
        labelContent += ESC_POS_COMMANDS.BARCODE_POSITION;
        
        // طباعة الباركود
        if (product.barcode.length === 13 && /^\d+$/.test(product.barcode)) {
            // EAN13 للأرقام فقط بطول 13
            labelContent += ESC_POS_COMMANDS.BARCODE_EAN13;
        } else {
            // Code128 للباقي
            labelContent += ESC_POS_COMMANDS.BARCODE_CODE128;
        }
        
        labelContent += String.fromCharCode(product.barcode.length) + product.barcode;
        labelContent += '\n\n';
        
        // رقم الباركود تحت الرمز
        labelContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        labelContent += product.barcode + '\n';
        
        // تغذية وقطع
        labelContent += '\n';
        labelContent += ESC_POS_COMMANDS.CUT_FEED;
        
        // إرسال للطباعة
        sendToThermalPrinter(labelContent, 'ملصق باركود');
        
        console.log('✅ تم إنشاء ملصق الباركود بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في طباعة ملصق الباركود:', error);
        app.showAlert('خطأ', 'فشل في طباعة ملصق الباركود: ' + error.message);
    }
}

/**
 * إرسال البيانات للطابعة الحرارية
 */
function sendToThermalPrinter(content, title) {
    try {
        // إنشاء نافذة طباعة مخفية
        const printWindow = window.open('', '_blank', 'width=300,height=400');
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 12px;
                        line-height: 1.2;
                        margin: 0;
                        padding: 5px;
                        white-space: pre-wrap;
                        direction: ltr;
                    }
                    @media print {
                        body { margin: 0; padding: 0; }
                        @page { margin: 0; size: 58mm auto; }
                    }
                </style>
            </head>
            <body>${content}</body>
            </html>
        `);
        
        printWindow.document.close();
        
        // طباعة تلقائية
        setTimeout(() => {
            printWindow.print();
            setTimeout(() => {
                printWindow.close();
            }, 1000);
        }, 500);
        
    } catch (error) {
        console.error('خطأ في إرسال البيانات للطابعة:', error);

        // طريقة بديلة - عرض المحتوى في نافذة
        const fallbackWindow = window.open('', '_blank');
        fallbackWindow.document.write(`
            <html dir="rtl">
            <head><title>${title}</title></head>
            <body style="font-family: monospace; white-space: pre-wrap; padding: 20px;">
                <h3>${title}</h3>
                <div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9;">
                    ${content.replace(/\n/g, '<br>')}
                </div>
                <br>
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </body>
            </html>
        `);
    }
}

/**
 * دوال مساعدة للتنسيق
 */

/**
 * تنسيق التاريخ للطباعة الحرارية
 */
function formatDateForThermal(date) {
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    return date.toLocaleDateString('ar-SA', options);
}

/**
 * تنسيق العملة للطباعة الحرارية
 */
function formatCurrencyForThermal(amount) {
    return parseFloat(amount).toFixed(2) + ' ج.م';
}

/**
 * قطع النص ليناسب العرض المحدد
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - 3) + '...';
}

/**
 * إضافة مسافات للنص ليصل للطول المطلوب
 */
function padText(text, length, align = 'left') {
    text = text.toString();
    if (text.length >= length) {
        return text.substring(0, length);
    }

    const padding = ' '.repeat(length - text.length);

    switch (align) {
        case 'right':
            return padding + text;
        case 'center':
            const leftPad = Math.floor(padding.length / 2);
            const rightPad = padding.length - leftPad;
            return ' '.repeat(leftPad) + text + ' '.repeat(rightPad);
        default: // left
            return text + padding;
    }
}

/**
 * الحصول على نص طريقة الدفع
 */
function getPaymentMethodText(method) {
    const methods = {
        'cash': 'نقدي',
        'card': 'بطاقة ائتمان',
        'bank': 'تحويل بنكي',
        'installment': 'تقسيط'
    };
    return methods[method] || 'غير محدد';
}

/**
 * طباعة فاتورة عادية (A4) - محسنة
 */
function printStandardInvoice(saleData) {
    try {
        console.log('🖨️ بدء طباعة الفاتورة العادية...');

        const sale = typeof saleData === 'string' ? db.getSale(saleData) : saleData;
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        const customer = sale.customerId !== 'guest' ? db.getCustomer(sale.customerId) : null;
        const settings = db.getSettings();
        const companyInfo = settings.company || {};

        // إنشاء رقم فاتورة
        const invoiceNumber = sale.invoiceNumber || `INV-${new Date().getFullYear()}-${sale.id.substring(0, 8)}`;
        const currentDate = new Date(sale.date);
        const hijriDate = db.toHijriDate ? db.toHijriDate(currentDate) : '';

        const invoiceContent = `
            <div class="invoice">
                <div class="invoice-header">
                    <div class="company-info">
                        <h1>${companyInfo.companyName || 'متجر تكنوفلاش'}</h1>
                        ${companyInfo.phone ? `<p class="phone">هاتف: ${companyInfo.phone}</p>` : ''}
                        ${companyInfo.address ? `<p class="address">${companyInfo.address}</p>` : ''}
                        ${companyInfo.email ? `<p class="email">البريد الإلكتروني: ${companyInfo.email}</p>` : ''}
                        ${companyInfo.taxNumber ? `<p class="tax-number">الرقم الضريبي: ${companyInfo.taxNumber}</p>` : ''}
                        ${companyInfo.commercialRegister ? `<p class="commercial-register">السجل التجاري: ${companyInfo.commercialRegister}</p>` : ''}
                    </div>
                </div>

                <div class="invoice-info">
                    <h2>فاتورة مبيعات</h2>
                    <div class="invoice-details">
                        <p><strong>رقم الفاتورة:</strong> ${invoiceNumber}</p>
                        <p><strong>التاريخ الميلادي:</strong> ${db.formatDate(sale.date, true)}</p>
                        ${hijriDate ? `<p><strong>التاريخ الهجري:</strong> ${hijriDate}</p>` : ''}
                        <p><strong>الوقت:</strong> ${currentDate.toLocaleTimeString('ar-SA')}</p>
                    </div>
                </div>

                ${customer ? `
                <div class="customer-info">
                    <h3>بيانات العميل</h3>
                    <p><strong>الاسم:</strong> ${customer.name}</p>
                    ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                    ${customer.email ? `<p><strong>البريد الإلكتروني:</strong> ${customer.email}</p>` : ''}
                    ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                </div>
                ` : ''}

                <div class="items-table">
                    <table>
                        <thead>
                            <tr>
                                <th>م</th>
                                <th>اسم المنتج</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map((item, index) => {
                                const product = db.getProduct(item.productId);
                                const productName = product ? product.name : 'منتج غير معروف';
                                const itemTotal = item.quantity * item.price;

                                return `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td>${productName}</td>
                                        <td>${item.quantity}</td>
                                        <td>${db.formatCurrency(item.price)}</td>
                                        <td>${db.formatCurrency(itemTotal)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-totals">
                    <div class="totals-row">
                        <span class="label">المجموع الفرعي:</span>
                        <span class="value">${db.formatCurrency(sale.subtotal || sale.total)}</span>
                    </div>
                    ${sale.tax && sale.tax > 0 ? `
                    <div class="totals-row">
                        <span class="label">الضريبة:</span>
                        <span class="value">${db.formatCurrency(sale.tax)}</span>
                    </div>
                    ` : ''}
                    ${sale.discount && sale.discount > 0 ? `
                    <div class="totals-row">
                        <span class="label">الخصم:</span>
                        <span class="value">-${db.formatCurrency(sale.discount)}</span>
                    </div>
                    ` : ''}
                    <div class="totals-row total-final">
                        <span class="label">الإجمالي النهائي:</span>
                        <span class="value">${db.formatCurrency(sale.total)}</span>
                    </div>
                </div>

                <div class="payment-info">
                    <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(sale.paymentMethod)}</p>
                    ${sale.amountPaid && sale.amountPaid !== sale.total ? `
                        <p><strong>المبلغ المدفوع:</strong> ${db.formatCurrency(sale.amountPaid)}</p>
                        ${sale.amountPaid > sale.total ? `<p><strong>الباقي:</strong> ${db.formatCurrency(sale.amountPaid - sale.total)}</p>` : ''}
                    ` : ''}
                </div>

                <div class="invoice-footer">
                    <div class="footer-content">
                        <p class="thank-you">شكراً لتعاملكم معنا</p>
                        <p class="return-policy">سياسة الإرجاع: يمكن إرجاع المنتجات خلال 7 أيام من تاريخ الشراء</p>
                    </div>
                    <div class="print-info">
                        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
                        <p>نظام تكنوفلاش لإدارة نقاط البيع</p>
                    </div>
                </div>
            </div>
        `;

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(createStandardInvoiceHTML(invoiceContent));
        printWindow.document.close();
        printWindow.print();

        console.log('✅ تم إنشاء الفاتورة العادية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في طباعة الفاتورة العادية:', error);
        app.showAlert('خطأ', 'فشل في طباعة الفاتورة العادية: ' + error.message);
    }
}

/**
 * إنشاء HTML للفاتورة العادية
 */
function createStandardInvoiceHTML(content) {
    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                * { box-sizing: border-box; }
                body {
                    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    background: #f5f5f5;
                    color: #333;
                }
                .invoice {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                .invoice-header {
                    text-align: center;
                    border-bottom: 3px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .company-info h1 {
                    color: #2c3e50;
                    margin: 0 0 10px 0;
                    font-size: 2.5em;
                    font-weight: bold;
                }
                .company-info p {
                    margin: 5px 0;
                    color: #666;
                    font-size: 1.1em;
                }
                .invoice-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 5px;
                }
                .invoice-info h2 {
                    color: #2c3e50;
                    margin: 0;
                    font-size: 1.8em;
                }
                .invoice-details p {
                    margin: 8px 0;
                    font-size: 1.1em;
                }
                .customer-info {
                    background: #e8f4f8;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .customer-info h3 {
                    color: #2c3e50;
                    margin: 0 0 15px 0;
                    font-size: 1.4em;
                }
                .customer-info p {
                    margin: 8px 0;
                    font-size: 1.1em;
                }
                .items-table {
                    margin-bottom: 30px;
                }
                .items-table table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 1.1em;
                }
                .items-table th,
                .items-table td {
                    padding: 12px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                .items-table th {
                    background: #2c3e50;
                    color: white;
                    font-weight: bold;
                }
                .items-table tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .items-table tbody tr:hover {
                    background: #e8f4f8;
                }
                .invoice-totals {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .totals-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    font-size: 1.2em;
                }
                .totals-row.total-final {
                    border-top: 2px solid #2c3e50;
                    margin-top: 15px;
                    padding-top: 15px;
                    font-weight: bold;
                    font-size: 1.4em;
                    color: #2c3e50;
                }
                .payment-info {
                    background: #e8f4f8;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .payment-info p {
                    margin: 8px 0;
                    font-size: 1.2em;
                }
                .invoice-footer {
                    text-align: center;
                    border-top: 2px solid #2c3e50;
                    padding-top: 20px;
                }
                .thank-you {
                    font-size: 1.5em;
                    color: #2c3e50;
                    font-weight: bold;
                    margin-bottom: 15px;
                }
                .return-policy {
                    color: #666;
                    font-size: 1.1em;
                    margin-bottom: 20px;
                }
                .print-info {
                    margin-top: 20px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    font-size: 0.9em;
                    color: #888;
                }
                .print-info p {
                    margin: 5px 0;
                }
                @media print {
                    body {
                        margin: 0;
                        padding: 0;
                        background: white;
                    }
                    .invoice {
                        border: none;
                        border-radius: 0;
                        box-shadow: none;
                        max-width: none;
                    }
                    @page {
                        margin: 1cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `;
}

/**
 * طباعة ملصقات متعددة للمنتجات
 */
function printMultipleBarcodeLabels(productIds) {
    try {
        console.log('🏷️ بدء طباعة ملصقات متعددة...');

        if (!productIds || productIds.length === 0) {
            throw new Error('لم يتم تحديد منتجات للطباعة');
        }

        let allLabelsContent = '';

        // تهيئة الطابعة
        allLabelsContent += ESC_POS_COMMANDS.INIT;

        productIds.forEach((productId, index) => {
            const product = db.getProduct(productId);
            if (!product || !product.barcode) {
                console.warn(`تم تخطي المنتج ${productId} - لا يحتوي على باركود`);
                return;
            }

            // فاصل بين الملصقات
            if (index > 0) {
                allLabelsContent += '\n' + '='.repeat(32) + '\n';
            }

            // اسم المنتج
            allLabelsContent += ESC_POS_COMMANDS.ALIGN_CENTER;
            allLabelsContent += ESC_POS_COMMANDS.BOLD_ON;
            const truncatedName = truncateText(product.name, 24);
            allLabelsContent += truncatedName + '\n';
            allLabelsContent += ESC_POS_COMMANDS.BOLD_OFF;

            // كود المنتج إن وجد
            if (product.code) {
                allLabelsContent += `كود: ${product.code}\n`;
            }

            // السعر
            allLabelsContent += ESC_POS_COMMANDS.BOLD_ON;
            allLabelsContent += `السعر: ${formatCurrencyForThermal(product.price)}\n`;
            allLabelsContent += ESC_POS_COMMANDS.BOLD_OFF;

            // إعدادات الباركود
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_HEIGHT(THERMAL_SETTINGS.BARCODE_HEIGHT);
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_WIDTH(THERMAL_SETTINGS.BARCODE_WIDTH);
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_POSITION;

            // طباعة الباركود
            if (product.barcode.length === 13 && /^\d+$/.test(product.barcode)) {
                allLabelsContent += ESC_POS_COMMANDS.BARCODE_EAN13;
            } else {
                allLabelsContent += ESC_POS_COMMANDS.BARCODE_CODE128;
            }

            allLabelsContent += String.fromCharCode(product.barcode.length) + product.barcode;
            allLabelsContent += '\n\n';

            // رقم الباركود
            allLabelsContent += ESC_POS_COMMANDS.ALIGN_CENTER;
            allLabelsContent += product.barcode + '\n\n';
        });

        // قطع نهائي
        allLabelsContent += ESC_POS_COMMANDS.CUT_FEED;

        // إرسال للطباعة
        sendToThermalPrinter(allLabelsContent, 'ملصقات الباركود');

        console.log('✅ تم إنشاء الملصقات المتعددة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في طباعة الملصقات المتعددة:', error);
        app.showAlert('خطأ', 'فشل في طباعة الملصقات المتعددة: ' + error.message);
    }
}
